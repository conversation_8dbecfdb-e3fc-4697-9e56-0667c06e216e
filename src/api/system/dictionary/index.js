import request from '@/utils/request'

/**
 * 分页查询字典列表
 */
export async function pageDictionaries (params) {
  const res = await request.get('/dictionary/page', { params })
  if (res.data.code === 200) {
    return res.data.list
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询字典列表
 */
export async function listDictionaries (params) {
  const res = await request.get('/dictionary/list', {
    params
  })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加字典
 */
export async function addDictionary (data) {
  const res = await request.post('/dictionary/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改字典
 */
export async function updateDictionary (data) {
  const res = await request.put('/dictionary/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除字典
 */
export async function removeDictionary (data) {
  const res = await request.delete('/dictionary/delete', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
