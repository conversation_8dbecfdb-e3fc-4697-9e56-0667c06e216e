import request from '@/utils/request'

/**
 * 分页查询
 */
export async function pageTagType (params) {
  const res = await request.get('/tag_type/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询列表
 */
export async function listTagType (params) {
  const res = await request.get('/tag_type/list', { params })
  if (res.data.code === 200 && res.data.data) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加
 */
export async function addTagType (data) {
  const res = await request.post('/tag_type/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改
 */
export async function updateTagType (data) {
  const res = await request.put('/tag_type/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除
 */
export async function removeTagType (data) {
  const res = await request.delete('/tag_type/delete', {data})
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}


/**
 * 分页查询
 */
export async function pageTag (params) {
    const res = await request.get('/tag/page', { params })
    if (res.data.code === 200) {
      return res.data.data
    }
    return Promise.reject(new Error(res.data.msg))
  }
  
  /**
   * 查询列表
   */
  export async function listTag (params) {
    const res = await request.get('/tag/list', { params })
    if (res.data.code === 200 && res.data.data) {
      return res.data.data
    }
    return Promise.reject(new Error(res.data.msg))
  }
  
  /**
   * 添加
   */
  export async function addTag (data) {
    const res = await request.post('/tag/create', data)
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  
  /**
   * 修改
   */
  export async function updateTag (data) {
    const res = await request.put('/tag/update', data)
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  
  /**
   * 删除
   */
  export async function removeTag (data) {
    const res = await request.delete('/tag/delete', {data})
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  
  