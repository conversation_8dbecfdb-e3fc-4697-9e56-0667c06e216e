import request from '@/utils/request'

/**
 * 分页查询角色
 */
export async function pageRoles (params) {
  const res = await request.get('/authRole/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询角色列表
 */
export async function listRoles (params) {
  const res = await request.get('/authRole/index', {
    params
  })
  if (res.data.code === 200 && res.data.data) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加角色
 */
export async function addRole (data) {
  const res = await request.post('/auth_role/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改角色
 */
export async function updateRole (data) {
  const res = await request.put('/auth_role/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除角色
 */
export async function removeRole (id) {
  const res = await request.delete('/auth_role/delete')
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 批量删除角色
 */
export async function removeRoles (data) {
  const res = await request.delete('/auth_role/delete', {
    data
  })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 获取角色分配的菜单
 */
export async function listRoleMenus (params) {
  const res = await request.get('/auth_role/rolemenu', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改角色菜单
 */
export async function updateRoleMenus (data) {
  const res = await request.put('/auth_role/rolemenu_update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
