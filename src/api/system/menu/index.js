import request from '@/utils/request'

/**
 * 分页查询菜单
 */
export async function pageMenus (params) {
  const res = await request.get('/system/menu/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询菜单列表
 */
export async function listMenus (params) {
  const res = await request.get('/auth_menu/page', {
    params
  })
  if (res.data.code === 200) {
    return res.data.data.list
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加菜单
 */
export async function addMenu (data) {
  const res = await request.post('/auth_menu/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改菜单
 */
export async function updateMenu (data) {
  const res = await request.put('/auth_menu/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除菜单
 */
export async function removeMenu (data) {
  console.log('data', data)
  const res = await request.delete('/auth_menu/delete', { data: data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
