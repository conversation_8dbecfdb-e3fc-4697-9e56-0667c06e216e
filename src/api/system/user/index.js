import request from '@/utils/request'

/**
 * 分页查询用户
 */
export async function pageUsers (params) {
  const res = await request.get('/admin/page', { params })
  if (res.data.code === 200) {
    return res.data.data.list
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询用户列表
 */
export async function listUsers (params) {
  const res = await request.get('/admin/index', {
    params
  })
  if (res.data.code === 200) {
    return res.data.data.list
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 根据id查询用户
 */
export async function getUser (params) {
  const res = await request.get('/admin/', params)
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加用户
 */
export async function addUser (data) {
  const res = await request.post('/admin/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改用户
 */
export async function updateUser (data) {
  const res = await request.put('/admin/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除用户
 */
export async function removeUser (data) {
  const res = await request.delete('/admin/delete', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 批量删除用户
 */
export async function removeUsers (data) {
  const res = await request.delete('/admin/delete', {
    data
  })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改用户状态
 */
export async function updateUserStatus (data) {
  const res = await request.put('/admin/status', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 重置用户密码
 */
export async function updateUserPassword (data) {
  const res = await request.put('/admin/password', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 导入用户
 */
export async function importUsers (file) {
  const formData = new FormData()
  formData.append('file', file)
  const res = await request.post('/system/user/import', formData)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 检查用户是否存在
 */
export async function checkExistence (field, value, id) {
  const res = await request.get('/system/user/existence', {
    params: { field, value, id }
  })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
