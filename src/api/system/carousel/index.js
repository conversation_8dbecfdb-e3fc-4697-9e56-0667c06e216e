import request from '@/utils/request'

/**
 * 分页查询
 */
export async function pageCarousel(params) {
  const res = await request.get('/lbt/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询列表
 */
export async function listCarousel(params) {
  const res = await request.get('/lbt/list', { params })
  if (res.data.code === 200 && res.data.data) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加
 */
export async function addCarousel(data) {
  const res = await request.post('/lbt/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改
 */
export async function updateCarousel(data) {
  const res = await request.put('/lbt/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除
 */
export async function deleteCarousel(data) {
  const res = await request.delete('/lbt/delete', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}



export function listType() {
  return [{id: 1, name: '小程序'}]
}