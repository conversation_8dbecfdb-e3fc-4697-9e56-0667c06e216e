import request from '@/utils/request'

export async function pageList (params) {
  const res = await request.post('/Firm/page', { ...params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

export async function deleteFirm (params) {
  const res = await request.post('/Firm/delete', { ...params })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

export async function createFirm (params) {
  const res = await request.post('/Firm/create', { ...params })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
export async function updateFirm (params) {
  const res = await request.post('/Firm/update', { ...params })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
export async function batchDelFirm (params) {
  const res = await request.post('/Firm/batchDel', { ...params })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}