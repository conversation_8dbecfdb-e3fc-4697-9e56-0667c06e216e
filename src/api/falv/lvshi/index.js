import request from '@/utils/request'

export async function pageList (params) {
  const res = await request.post('/Attorney/page', { ...params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

export async function deleteAttorney (params) {
  const res = await request.post('/Attorney/delete', { ...params })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
export async function getFirmList (params) {
    const res = await request.post('/Firm/page', { ...params,limit:100000 })
    if (res.data.code === 200) {
      return res.data.data
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function createAttorney (params) {
    const res = await request.post('/Attorney/create', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function updateAttorney (params) {
    const res = await request.post('/Attorney/update', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function batchDelAttorney (params) {
    const res = await request.post('/Attorney/batchDel', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  