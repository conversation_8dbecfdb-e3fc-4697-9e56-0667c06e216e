import request from '@/utils/request'

/**
 * 分页查询招聘列表
 */
export async function pageRecruit (params) {
  const res = await request.get('/recruit/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询招聘列表
 */
export async function listRecruit (params) {
  const res = await request.get('/recruit/list', {
    params
  })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加招聘
 */
export async function addRecruit (data) {
  const res = await request.post('/recruit/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改招聘
 */
export async function updateRecruit (data) {
  const res = await request.put('/recruit/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除招聘
 */
export async function deleteRecruit (data) {
  const res = await request.delete('/recruit/delete', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 审核招聘
 */
export async function auditRecruit (data) {
  const res = await request.put('/recruit/audit', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}