import request from '@/utils/request'

/**
 * 分页查询用户列表
 */
export async function pageUser (params) {
  const res = await request.get('/user/page', { params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 查询用户列表
 */
export async function listUser (params) {
  const res = await request.get('/user/list', {
    params
  })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 添加用户
 */
export async function addUser (data) {
  const res = await request.post('/user/create', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 修改用户
 */
export async function updateUser (data) {
  const res = await request.put('/user/update', data)
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

/**
 * 删除用户
 */
export async function removeUser (data) {
  const res = await request.delete('/user/delete', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

export async function batchDelUser (data) {
  const res = await request.delete('/user/batchDel', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}

export async function avatarUpload (data) {
  const res = await request.post('/user/avatarUpload', { data })
  if (res.data.code === 200) {
    return res.data.msg
  }
  return Promise.reject(new Error(res.data.msg))
}
export async function fileUpload (file, config, fileName) {
  const formData = new FormData();
  formData.append('file', file, fileName);
  const res = await request.post('/Upload/file', formData, config)
  if (res.data.code === 200 && res.data.data) {
    // console.log(res.data.data)
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}
// export async function uploadFile(file, config, fileName) {
//   const formData = new FormData();
//   formData.append('file', file, fileName);
//   const res = await request.post('/file/upload', formData, config);
//   if (res.data.code === 0 && res.data.data) {
//     return res.data.data;
//   }
//   return Promise.reject(new Error(res.data.message));
// }
