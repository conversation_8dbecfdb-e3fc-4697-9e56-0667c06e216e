import request from '@/utils/request'

/**
 * 分页查询招聘列表
 */
export async function pageList (params) {
  const res = await request.post('/Merchant/page', { ...params })
  if (res.data.code === 200) {
    return res.data.data
  }
  return Promise.reject(new Error(res.data.msg))
}
export async function getTagList (params) {
    const res = await request.post('/Tag/list', { ...params })
    if (res.data.code === 200) {
      return res.data.data
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function addMerchant (params) {
    const res = await request.post('/Merchant/create', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function updateMerchant (params) {
    const res = await request.post('/Merchant/update', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function deleteMerchant (params) {
    const res = await request.post('/Merchant/delete', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function batchDelMerchant (params) {
    const res = await request.post('/Merchant/batchDel', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function pageGoods (params) {
    const res = await request.post('/Goods/page', { ...params })
    if (res.data.code === 200) {
      return res.data.data
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function updateGoods (params) {
    const res = await request.post('/Goods/update', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function addGoods (params) {
    const res = await request.post('/Goods/create', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function deleteGoods (params) {
    const res = await request.post('/Goods/delete', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }
  export async function batchDelGoods (params) {
    const res = await request.post('/Goods/batchDel', { ...params })
    if (res.data.code === 200) {
      return res.data.msg
    }
    return Promise.reject(new Error(res.data.msg))
  }


