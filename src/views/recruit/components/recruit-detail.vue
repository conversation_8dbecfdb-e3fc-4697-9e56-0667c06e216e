<!-- 详情弹窗 -->
<template>
  <ele-modal title="职位详情" :width="600" v-model="visible">
    <el-space wrap v-if="data.auditStatus===0" style="margin-bottom:10px;">
      <el-button type="primary" @click.stop="audit(1)">通过审核</el-button>
      <el-button type="danger" @click.stop="audit(2)">驳回</el-button>
    </el-space>
    <el-descriptions v-if="data" :border="true" :column="2" class="detail-table">
      <el-descriptions-item label="招聘公司" :span="2">
        <div style="word-break: break-all">{{ data.companyName }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="招聘部门">
        <div>{{ data.group }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="招聘岗位">
        <div>{{ data.manage }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="招聘人数">
        <div>{{ data.personnum }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="到岗时间">
        <div>{{ data.arriveTime }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="工资">
        <div>{{ data.salary }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="发布人">
        <div>{{ data.createUser }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="公司地址" :span="2">
        <div>{{ data.address }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="岗位职责" :span="2">
        <div>{{ data.value }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="任职要求" :span="2">
        <div>{{ data.content }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="联系人">
        <div>{{ data.name }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">
        <div>{{ data.tel }}</div>
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>
  
<script setup>
import { ElMessageBox } from 'element-plus/es'
import { EleMessage } from 'ele-admin-plus/es'
import { auditRecruit } from '@/api/recruit'

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
})

const emit = defineEmits(['done'])

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean })

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false
}

const audit = (status) => {
  ElMessageBox.confirm(
  `${status===1?'确定要通过审核？':'确定要驳回？'}`, 
  '系统提示', {
  type: 'warning',
  draggable: true
})
  .then(() => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    })
    auditRecruit({ auditStatus: status, id: props.data.id })
      .then((msg) => {
        loading.close()
        EleMessage.success(msg)
        handleCancel()
        emit('done')
      })
      .catch((e) => {
        loading.close()
        EleMessage.error(e.message)
      })
  })
  .catch((error) => { console.log(error.message )})
}
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 88px;
    text-align: right;
    font-weight: normal;
  }

  .detail-table :deep(.el-descriptions__content > div) {
    max-height: 100%;
  }
</style>
