<!-- 用户编辑弹窗 -->
<template>
  <ele-modal form :width="600" v-model="visible" :title="isUpdate ? '修改职位' : '发布职位'" @open="handleOpen">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" @submit.prevent="">
      <el-row :gutter="16">
        <el-col :sm="24" :xs="24">
          <el-form-item label="招聘公司" prop="companyName">
            <el-input clearable :maxlength="100" v-model="form.companyName" placeholder="请输入招聘公司" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="招聘部门" prop="group">
            <el-input clearable :maxlength="30" v-model="form.group" placeholder="请输入招聘部门" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="招聘岗位" prop="manage">
            <el-input clearable :maxlength="50" v-model="form.manage" placeholder="请输入招聘岗位" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="招聘人数" prop="personnum">
            <el-input-number clearable v-model="form.personnum" placeholder="请输入招聘人数" :min="1" :max="999" :precision="0" controls-position="right" class="number" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="到岗时间" prop="arriveTime">
            <el-date-picker v-model="form.arriveTime" type="date" placeholder="到岗时间" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="工资" prop="salary">
            <el-input-number clearable v-model="form.salary" placeholder="请输入工资" :min="1" :max="999999" :precision="2" controls-position="right" class="number" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="发布人" prop="createUser">
            <el-input clearable :maxlength="30" v-model="form.createUser" placeholder="请输入发布人" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="公司地址" prop="address">
            <el-input clearable :maxlength="100" v-model="form.address" placeholder="请输入公司地址" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="岗位职责" prop="value">
            <el-input type="textarea" :rows="3" clearable :maxlength="500" v-model="form.value" placeholder="请输入岗位职责" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="任职要求" prop="content">
            <el-input type="textarea" :rows="3" clearable :maxlength="500" v-model="form.content" placeholder="请输入任职要求" />
          </el-form-item>
        </el-col>

        <el-col :sm="12" :xs="12">
          <el-form-item label="联系人" prop="name">
            <el-input clearable :maxlength="30" v-model="form.name" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="12">
          <el-form-item label="联系电话" prop="tel">
            <el-input clearable :maxlength="30" v-model="form.tel" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { EleMessage } from 'ele-admin-plus/es'
import { useFormData } from '@/utils/use-form-data'
import { addRecruit, updateRecruit } from '@/api/recruit'

const props = defineProps({
  /** 修改回显的数据 */
  data: Object,
  /** 添加时机构id */
  organizationId: Number
})

const emit = defineEmits(['done'])

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean })

/** 是否是修改 */
const isUpdate = ref(false)

/** 提交状态 */
const loading = ref(false)

/** 表单实例 */
const formRef = ref(null)

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  userId: void 0,
  companyName: '',
  group: '',
  groups: [],
  manage: '',
  personnum: void 1,
  arriveTime: '',
  salary: void 0,
  createUser: '',
  address: '',
  value: '',
  content: '',
  name: '',
  tel: '',
  auditStatus: void 0,
})

/** 表单验证规则 */
const rules = reactive({
  companyName: [
    {
      required: true,
      message: '请输入招聘公司',
      type: 'string',
      trigger: 'blur'
    },
    {
      min: 2,
      message: '招聘公司长度最少为2位',
      type: 'string',
      trigger: 'blur'
    },
    {
      max: 100,
      message: '招聘公司长度最多为100位',
      type: 'string',
      trigger: 'blur'
    },
  ],
  group: [
    {
      required: true,
      message: '请输入招聘部门',
      type: 'string',
      trigger: 'blur'
    },
    {
      min: 2,
      message: '招聘部门长度最少为2位',
      type: 'string',
      trigger: 'blur'
    },
    {
      max: 30,
      message: '招聘部门长度最多为30位',
      type: 'string',
      trigger: 'blur'
    }
  ],
  manage: [
    {
      required: true,
      message: '请输入招聘岗位',
      type: 'string',
      trigger: 'blur'
    },
    {
      min: 2,
      message: '招聘岗位长度最少为2位',
      type: 'string',
      trigger: 'blur'
    },
    {
      max: 50,
      message: '招聘岗位长度最多为50位',
      type: 'string',
      trigger: 'blur'
    }
  ],
  personnum: [
    {
      required: true,
      message: '请输入招聘人数',
      type: 'number',
      trigger: 'blur'
    },
    {
      min: 1,
      message: '招聘人数最少为1位',
      type: 'number',
      trigger: 'blur'
    },
    {
      max: 100,
      message: '招聘人数最多为100位',
      type: 'number',
      trigger: 'blur'
    }
  ],
  name: [
    {
      required: true,
      message: '请输入联系人',
      type: 'string',
      trigger: 'blur'
    },
    {
      min: 2,
      message: '联系人长度最少为2位',
      type: 'string',
      trigger: 'blur'
    },
    {
      max: 30,
      message: '联系人长度最多为30位',
      type: 'string',
      trigger: 'blur'
    }
  ],
  arriveTime: [
    {
      required: true,
      message: '请输入招聘时间',
      type: 'string',
      trigger: 'blur'
    }
  ],
  salary: [
    {
      required: true,
      message: '请输入工资',
      type: 'number',
      trigger: 'blur'
    }
  ],
  address: [
    {
      required: true,
      message: '请输入公司地址',
      type: 'string',
      trigger: 'blur'
    }
  ],
  value: [
    {
      required: true,
      message: '请输入岗位职责',
      type: 'string',
      trigger: 'blur'
    }
  ],
  content: [
    {
      required: true,
      message: '请输入任职要求',
      type: 'string',
      trigger: 'blur'
    }
  ],
  tel: [
    {
      required: true,
      message: '请输入联系电话',
      type: 'string',
      trigger: 'blur'
    }
  ]
})

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false
}

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    const saveOrUpdate = isUpdate.value ? updateRecruit : addRecruit
    saveOrUpdate(form)
      .then((msg) => {
        loading.value = false
        EleMessage.success(msg)
        handleCancel()
        emit('done')
      })
      .catch((e) => {
        loading.value = false
        EleMessage.error(e.message)
      })
  })
}

/** 弹窗打开事件 */
const handleOpen = () => {
  if (props.data) {
    assignFields({ ...props.data, password: '' })
    isUpdate.value = true
  } else {
    resetFields()
    form.organizationId = props.organizationId
    isUpdate.value = false
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.()
    })
  })
};
</script>
<style lang="scss" scoped>
::v-deep .number {
  .el-input__inner{
    text-align: left !important;
  }
}
</style>