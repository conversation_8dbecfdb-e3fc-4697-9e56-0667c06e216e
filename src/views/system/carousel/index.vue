<template>
  <ele-page :flex-table="fixedHeight">
    <ele-card :flex-table="fixedHeight" :body-style="{ paddingBottom: '4px' }" :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '0px' : void 0
      }">
      <!-- 提示信息 -->
      <ele-alert show-icon :closable="false" :style="{ marginBottom: toolDefault ? '12px' : '4px' }">
        <template #title>
          <span>已选择&nbsp;</span>
          <ele-text strong type="primary" tag="span">
            {{ selections.length }}
          </ele-text>
          <span>&nbsp;项数据&emsp;</span>

          <el-link type="primary" :underline="false" style="vertical-align: 0px" @click="clearChoose">
            清空
          </el-link>
        </template>
      </ele-alert>
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="id" :columns="columns" :datasource="datasource" v-model:current="current" v-model:selections="selections" :show-overflow-tooltip="true" :export-config="exportConfig" :print-config="{ datasource: exportSource }"
        :border="bordered" :sticky="!fixedHeight" :toolbar="{ theme: toolDefault ? 'default' : 'plain' }" :default-sort="{ prop: 'createTime', order: 'ascending' }" :footer-style="{ paddingBottom: '12px' }"
        @done="handleDone">
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon" @click="openEdit()">
            新增图片
          </el-button>
          <el-button type="danger" class="ele-btn-icon" @click="remove()">
            批量删除
          </el-button>
        </template>
        <!-- 表头工具按钮 -->
        <template #tools>
          <div style="display: flex; align-items: center; flex-wrap: wrap">
            <div>边框&nbsp;</div>
            <el-switch v-model="bordered" size="small" />
            <el-divider direction="vertical" />
            <div>表头背景&nbsp;</div>
            <el-switch v-model="toolDefault" size="small" />
            <el-divider direction="vertical" />
            <div>高度铺满&nbsp;</div>
            <el-switch v-model="fixedHeight" size="small" />
            <el-divider direction="vertical" />
          </div>
        </template>

        <template #img="{ row }">
          <el-image :src="row.img.fullUrl" fit="fill" style="width: 100%; height: 80px" />
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click.stop="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click.stop="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit-form :data="current" v-model="showEdit" @done="reload" />
  </ele-page>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessageBox } from 'element-plus/es'
import { EleMessage } from 'ele-admin-plus/es'
import { getExportWorkbook } from '@/config/use-global-config'
import { download } from '@/utils/common'
import request from '@/utils/request'
import { pageCarousel, listCarousel, deleteCarousel } from '@/api/system/carousel'
import EditForm from './components/edit-form.vue'


defineOptions({ name: 'ListCarousel' })

/** 表格实例 */
const tableRef = ref(null)

/** 用户名筛选值 */
const nicknameFilterValue = ref('')

/** 表格搜索参数 */
const lastWhere = reactive({})

/** 表格列配置 */
const columns = computed(() => {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '#',
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '图片名称',
      minWidth: 150,
      align: 'center',
    },
    {
      prop: 'createUser',
      label: '发布人',
      minWidth: 110,
      align: 'center',
    },
    {
      prop: 'type',
      label: '类型',
      minWidth: 100,
      align: 'center',
    },
    {
      prop: 'img',
      label: '图片 ',
      minWidth: 200,
      align: 'center',
      slot: 'img'
    },
    {
      prop: 'createTime',
      label: '发布时间',
      minWidth: 200,
      align: 'center',
    },
    {
      prop: 'updateTime',
      label: '更新时间',
      width: 200,
      align: 'center',
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      hideInPrint: true,
      hideInExport: true
    }
  ]
})

/** 表格选中数据 */
const selections = ref([])

/** 表格单选选中数据 */
const current = ref(null)

/** 是否显示编辑弹窗 */
const showEdit = ref(false)

/**是否显示详情弹窗 */
const showDetail = ref(false)

/** 表格是否显示边框 */
const bordered = ref(true)

/** 表头工具栏风格 */
const toolDefault = ref(true)

/** 表格固定高度 */
const fixedHeight = ref(false)

/** 表格数据源 */
const datasource = ({ pages, where, orders, filters }) => {
  return pageCarousel({ ...where, ...orders, ...filters, ...pages })
}

/** 表格数据请求完成事件 */
const handleDone = () => {
  // 回显 id 为 45、47、48 的数据的复选框
  nextTick(() => {
    //const ids = [45, 47, 48]
    //tableRef.value?.setSelectedRowKeys?.(ids)
  })
}

/** 搜索事件 */
const handleSearch = (where) => {
  Object.assign(lastWhere, where)
  doReload()
}

/** 刷新表格 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where })
}

/** 清空选择 */
const clearChoose = () => {
  tableRef.value?.clearSelection?.()
}

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null
  showEdit.value = true
}
/** 打开详情弹窗 */
const openDetail = (row) => {
  current.value = row ?? null
  showDetail.value = true
}
/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row]
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据')
    return
  }

  ElMessageBox.confirm(
    `确定要删除“${rows.map((d) => d.name).join(', ')}”吗?`, 
    '系统提示', {
    type: 'warning',
    draggable: true
  })
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      })
      deleteCarousel({ ids: rows.map((d)=>(d.id)) })
        .then((msg) => {
          loading.close()
          EleMessage.success(msg)
          reload()
        })
        .catch((e) => {
          loading.close()
          EleMessage.error(e.message)
        })
    })
    .catch(() => { })
}

/** 表格搜索 */
const doReload = () => {
  if (nicknameFilterValue.value) {
    reload({
      ...lastWhere,
      nickname: nicknameFilterValue.value
    })
  } else {
    reload(lastWhere)
  }
}

/** 导出和打印全部数据的数据源 */
const exportSource = ({ where, orders, filters }) => {
  return listCarousel({ ...where, ...orders, ...filters })
}

/** 导出配置 */
const exportConfig = reactive({
  fileName: '基础列表数据',
  datasource: exportSource,
  beforeExport: (params) => {
    const { fileName, closeModal, bodyCols, bodyData, headerData } = params
    const workbook = getExportWorkbook(params)
    const sheet = workbook.getWorksheet('Sheet1')

    const getBuffer = async () => {
      // 添加头像列图片
      const avatarColIndex = bodyCols.findIndex(
        (c) => c.dataKey === 'avatar'
      )
      if (sheet != null && avatarColIndex !== -1) {
        const avatarBuffers = await Promise.all(
          bodyData.map(async (row) => {
            const url = row[avatarColIndex].text
            if (!url) {
              return
            }
            const res = await request({ url, responseType: 'arraybuffer' })
            return res.data
          })
        )
        avatarBuffers.forEach((buffer, index) => {
          const rowIndex = index + headerData.length
          if (buffer != null) {
            const imgId = workbook.addImage({ buffer, extension: 'png' })
            sheet.addImage(imgId, {
              tl: { col: avatarColIndex + 0.4, row: rowIndex + 0.2 },
              ext: { width: 48, height: 48 }
            })
            sheet.getCell(rowIndex + 1, avatarColIndex + 1).value = ''
          }
          sheet.getRow(rowIndex + 1).height = 42
          sheet.getColumn(avatarColIndex + 1).width = 8
        })
      }
      // 输出workbook
      const data = await workbook.xlsx.writeBuffer()
      return data
    }

    getBuffer().then((data) => {
      download(data, `${fileName}.xlsx`)
      closeModal()
    })
    return false
  }
});
</script>

<style lang="scss" scoped>

</style>
