<!-- 用户编辑弹窗 -->
<template>
    <ele-modal form :width="400" v-model="visible" :title="isUpdate ? '修改轮播图' : '新建轮播图'" @open="handleOpen">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" @submit.prevent="">
        <el-row :gutter="16">
          <el-col :sm="24" :xs="24">
            <el-form-item label="图片名称" prop="name">
              <el-input clearable :maxlength="30" v-model="form.name" placeholder="请输入图片名称" />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :xs="24">
            <el-form-item label="图片类型" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :xs="24">
            <el-form-item label="图片上传" prop="img">
              <el-upload
              :http-request="handleUpload"
              :show-file-list="false"
              :multiple="false"
              accept=".gif,.jpg,.png,.jpeg"
              class="carousel-upload"
            >
              <el-image :src="form.img.fullUrl">
                <template #error>
                  <div class="image-slot">
                    <el-icon><icon-picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          保存
        </el-button>
      </template>
    </ele-modal>
  </template>
  
  <script setup>
  import { ref, reactive, nextTick } from 'vue'
  import { EleMessage } from 'ele-admin-plus/es'
  import { useFormData } from '@/utils/use-form-data'
  import { addCarousel, updateCarousel, listType } from '@/api/system/carousel'
  import { Picture as IconPicture } from '@element-plus/icons-vue'
import { uploadFile } from '@/api/system/file'

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object,
  })
  
  const emit = defineEmits(['done'])

  const typeList = listType();
  
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean })
  
  /** 是否是修改 */
  const isUpdate = ref(false)
  
  /** 提交状态 */
  const loading = ref(false)
  
  /** 表单实例 */
  const formRef = ref(null)
  
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    img: {},
    type: void 0,
  })
  
  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入图片名称',
        type: 'string',
        trigger: 'blur'
      },
      {
        min: 1,
        message: '图片名称长度最少为1位',
        type: 'string',
        trigger: 'blur'
      },
      {
        max: 30,
        message: '图片名称长度最多为30位',
        type: 'string',
        trigger: 'blur'
      },
    ],
    type: [
      {
        required: true,
        message: '请选择图片类型',
        type: 'number',
        trigger: 'blur'
      }
    ],
    img: [
      {
        required: true,
        message: '请上传图片',
        type: 'object',
        trigger: 'blur'
      }
    ],
  })
  
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false
  }
  
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return
      }
      loading.value = true
      const saveOrUpdate = isUpdate.value ? updateCarousel : addCarousel
      saveOrUpdate(form)
        .then((msg) => {
          loading.value = false
          EleMessage.success(msg)
          handleCancel()
          emit('done')
        })
        .catch((e) => {
          loading.value = false
          EleMessage.error(e.message)
        })
    })
  }
  
  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields({ ...props.data, password: '' })
      isUpdate.value = true
    } else {
      resetFields()
      form.organizationId = props.organizationId
      isUpdate.value = false
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.()
      })
    })
  };

  /** 上传事件 */
  const handleUpload = (data, retry) => {
    if (!data.file) {
      return;
    }
    uploadFile(data.file)
      .then((res) => {
        form.img = res
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  </script>
  <style lang="scss" scoped>
  ::v-deep .number {
    .el-input__inner{
      text-align: left !important;
    }
  }
  ::v-deep .carousel-upload{
    width: 100%;
    background:#f5f5f5;
    height: 80px;
    overflow: hidden;
    .el-upload {
      width: 100%;
    }
    .el-icon{
      width: 80px;
      height: 80px;
      font-size: 40px;
      color: #ccc;
    }
  }
  </style>