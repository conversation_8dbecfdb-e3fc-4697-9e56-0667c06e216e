<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="用户账号">
            <el-input clearable v-model.trim="form.username" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="IP地址">
            <el-input clearable v-model.trim="form.ip" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24" v-if="searchExpand">
          <el-form-item label="操作类型">
            <el-input clearable v-model.trim="form.title" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24" v-if="searchExpand">
          <el-form-item label="操作时间">
            <el-date-picker unlink-panels type="daterange" v-model="dateRange" range-separator="-" value-format="YYYY-MM-DD" start-placeholder="开始时间" end-placeholder="结束时间" class="ele-fluid" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleExpand" style="margin-left: 12px">
              <template v-if="searchExpand">
                <span>收起</span>
                <el-icon style="vertical-align: -1px">
                  <ArrowUp />
                </el-icon>
              </template>
              <template v-else>
                <span>展开</span>
                <el-icon style="vertical-align: -2px">
                  <ArrowDown />
                </el-icon>
              </template>
            </el-link>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue'
import { useFormData } from '@/utils/use-form-data'

const emit = defineEmits(['search'])

/** 表单数据 */
const [form, resetFields] = useFormData({
  username: '',
  module: ''
})

/** 搜索表单是否展开 */
const searchExpand = ref(false)

/** 日期范围 */
const dateRange = ref(['', ''])

/** 搜索 */
const search = () => {
  const [startDate, endDate] = dateRange.value || []
  emit('search', { ...form, startDate, endDate })
}

/** 搜索展开/收起 */
const toggleExpand = () => {
  searchExpand.value = !searchExpand.value
}

/**  重置 */
const reset = () => {
  resetFields()
  dateRange.value = ['', '']
  search()
};
</script>
