<!-- 详情弹窗 -->
<template>
  <ele-modal title="详情" :width="720" v-model="visible">
    <el-descriptions v-if="data" :border="true" :column="mobile ? 1 : 2" class="detail-table">
      <el-descriptions-item label="操作人">
        <div>{{ data.nickname }}({{ data.username }})</div>
      </el-descriptions-item>
      <el-descriptions-item label="IP地址">
        <div>{{ data.ip }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作类型">
        <div>{{ data.title }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作时间">
        <div>{{ data.createtime }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="浏览器" :span="2">
        <div>{{ data.useragent }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="请求地址" :span="2">
        <div style="word-break: break-all">{{ data.url }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="请求数据" :span="2">
        <div>{{ data.content }}</div>
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>

<script setup>
import { reactive } from 'vue'
import { useMobile } from '@/utils/use-mobile'

defineProps({
  /** 修改回显的数据 */
  data: Object
})

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean })

/** 文字省略组件的提示组件的属性 */
const ellipsisTooltipProps = reactive({
  popperStyle: {
    width: '580px',
    maxWidth: '90%',
    wordBreak: 'break-all'
  },
  bodyStyle: {
    maxWidth: 'calc(100vw - 32px)',
    maxHeight: '252px',
    overflowY: 'auto',
    '--ele-scrollbar-color': '#5e5e5e',
    '--ele-scrollbar-hover-color': '#707070',
    '--ele-scrollbar-size': '8px'
  },
  offset: 4,
  placement: 'top'
})

/** 是否是移动端 */
const { mobile } = useMobile();
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 88px;
    text-align: right;
    font-weight: normal;
  }

  .detail-table :deep(.el-descriptions__content > div) {
    max-height: 100%;
  }
</style>
