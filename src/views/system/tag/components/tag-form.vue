<!-- 用户编辑弹窗 -->
<template>
    <ele-modal form :width="300" v-model="visible" :title="isUpdate ? '修改标签' : '新建标签'" @open="handleOpen">
      <el-form label-suffix=":" ref="formRef" :model="form" :rules="rules" label-width="auto" @submit.prevent="">
        <el-row :gutter="16">
          <el-col :sm="24" :xs="24">
            <el-form-item label="标签名称" prop="tagName">
              <el-input clearable :maxlength="50" v-model="form.tagName" placeholder="请输入标签名称" />
            </el-form-item>
          </el-col>
          <el-col :sm="24" :xs="24">
            <el-form-item label="排序值" prop="sortNumber">
              <el-input-number class="number" controls-position="right" clearable :min="0" v-model="form.sortNumber" placeholder="请输入排序值" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          保存
        </el-button>
      </template>
    </ele-modal>
  </template>
  
  <script setup>
  import { ref, reactive, nextTick } from 'vue'
  import { EleMessage } from 'ele-admin-plus/es'
  import { useFormData } from '@/utils/use-form-data'
  import { addTag, updateTag } from '@/api/system/tag'
  
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object,
    /** 添加时分类id */
    type: Number
  })
  
  const emit = defineEmits(['done'])
  
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean })
  
  /** 是否是修改 */
  const isUpdate = ref(false)
  
  /** 提交状态 */
  const loading = ref(false)
  
  /** 表单实例 */
  const formRef = ref(null)
  
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    tagName: '',
    sortNumber: void 0,
    type: props.type || void 0,
  })
  
  /** 表单验证规则 */
  const rules = reactive({
    tagName: [
      {
        required: true,
        message: '请输入标签名称',
        type: 'string',
        trigger: 'blur'
      },
      {
        min: 1,
        message: '标签名称长度最少为1位',
        type: 'string',
        trigger: 'blur'
      },
      {
        max: 50,
        message: '标签名称长度最多为50位',
        type: 'string',
        trigger: 'blur'
      },
    ],
  })
  
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false
  }
  
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return
      }
      loading.value = true
      const saveOrUpdate = isUpdate.value ? updateTag : addTag
      saveOrUpdate(form)
        .then((msg) => {
          loading.value = false
          EleMessage.success(msg)
          handleCancel()
          emit('done')
        })
        .catch((e) => {
          loading.value = false
          EleMessage.error(e.message)
        })
    })
  }
  
  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields({ ...props.data, password: '' })
      isUpdate.value = true
    } else {
      resetFields()
      form.organizationId = props.organizationId
      isUpdate.value = false
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.()
      })
    })
  };
  </script>
  <style lang="scss" scoped>
  ::v-deep .number {
    .el-input__inner{
      text-align: left !important;
    }
  }
  </style>