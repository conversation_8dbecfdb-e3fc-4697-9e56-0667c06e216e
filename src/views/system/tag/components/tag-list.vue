<template>
    <search-form ref="searchRef" style="margin-bottom: -14px" @search="reload" />
    <ele-pro-table ref="tableRef" row-key="id" :columns="columns" :datasource="datasource" :show-overflow-tooltip="true" v-model:selections="selections" :highlight-current-row="true" :export-config="{ fileName: '字典数据', datasource: exportSource }"
      :print-config="{ datasource: exportSource }" :style="{ paddingBottom: '16px' }">
      <template #toolbar>
        <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openEdit()">
          新建
        </el-button>
        <el-button type="danger" class="ele-btn-icon" :icon="DeleteOutlined" @click="remove()">
          批量删除
        </el-button>
      </template>
      <template #action="{ row }">
        <el-link type="primary" :underline="false" @click="openEdit(row)">
          修改
        </el-link>
        <el-divider direction="vertical" />
        <el-link type="danger" :underline="false" @click="remove(row)">
          删除
        </el-link>
      </template>
    </ele-pro-table>
    <data-edit v-model="showEdit" :data="current" :type="props.type" @done="reload" />
  </template>
  
  <script setup>
  import { ref, watch } from 'vue'
  import { ElMessageBox } from 'element-plus/es'
  import { EleMessage } from 'ele-admin-plus/es'
  import { PlusOutlined, DeleteOutlined } from '@/components/icons'
  import SearchForm from './search-form.vue'
  import DataEdit from './tag-form.vue'
  import {
    pageTag,
    removeTag,
    listTag
  } from '@/api/system/tag'
  
  const props = defineProps({
    /** 分类id */
    type: Number
  })
  
  /** 搜索栏实例 */
  const searchRef = ref(null)
  
  /** 表格实例 */
  const tableRef = ref(null)
  
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '#',
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'tagName',
      label: '标签名称',
      minWidth: 120
    },
    {
      prop: 'sortNumber',
      label: '排序号',
      width: 100,
      align: 'center'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 130,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ])
  
  /** 表格选中数据 */
  const selections = ref([])
  
  /** 当前编辑数据 */
  const current = ref(null)
  
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false)
  
  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return pageTag({
      ...where,
      ...orders,
      ...pages,
      type: props.type
    })
  }
  
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where })
  }
  
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null
    showEdit.value = true
  }
  
  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row]
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据')
      return
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.tagName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        })
        removeTag({ids: rows.map((d) => (d.id))})
          .then((msg) => {
            loading.close()
            EleMessage.success(msg)
            reload()
          })
          .catch((e) => {
            loading.close()
            EleMessage.error(e.message)
          })
      })
      .catch(() => { })
  }
  
  // 监听字典id变化
  watch(
    () => props.type,
    () => {
      searchRef.value?.resetFields?.()
      reload({})
    }
  )
  
  /** 导出和打印全部数据的数据源 */
  const exportSource = ({ where, orders }) => {
    return pageTag({
      ...where,
      ...orders,
    })
  };
  </script>
  