<template>
    <ele-page flex-table :multi-card="false" hide-footer style="min-height: 420px">
      <ele-card flex-table :body-style="{ padding: '0 0 0 16px', overflow: 'hidden' }">
        <ele-split-panel ref="splitRef" flex-table size="256px" allow-collapse :custom-style="{ borderWidth: '0 1px 0 0', padding: '16px 0' }" :body-style="{ padding: '16px 16px 0 0', overflow: 'hidden' }" :style="{ height: '100%', overflow: 'visible' }">
          <div style="padding: 0 16px 12px 0">
            <el-input clearable :maxlength="20" v-model="keywords" placeholder="输入名称搜索" :prefix-icon="SearchOutlined" />
          </div>
          <div style="margin-bottom: 12px">
            <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openTypeEdit()">
              新建
            </el-button>
            <el-button type="warning" :disabled="!current" class="ele-btn-icon" :icon="EditOutlined" @click="openTypeEdit(current)">
              修改
            </el-button>
            <el-button type="danger" :disabled="!current" class="ele-btn-icon" :icon="DeleteOutlined" @click="remove">
              删除
            </el-button>
          </div>
          <ele-loading :loading="loading" :style="{ flex: 1, paddingRight: '16px', overflow: 'auto' }">
            <el-tree ref="treeRef" :data="data" highlight-current node-key="id" :props="{ label: 'name' }" :expand-on-click-node="false" :default-expand-all="true" :filter-node-method="filterNode" :style="{
                '--ele-tree-item-height': '34px',
                '--ele-tree-expand-padding': 0,
                '--ele-tree-expand-margin': 0
              }" @node-click="handleNodeClick">
              <template #default="{ data: d }">
                <div class="el-tree-node__label" style="display: flex; align-items: center">
                  <div style="margin-right: 4px">{{ d.name }}</div>
                </div>
              </template>
            </el-tree>
          </ele-loading>
          <template #body>
            <tag-list v-if="current && current.id" :type="current.id" />
          </template>
        </ele-split-panel>
      </ele-card>
      <tag-form v-model="showTagForm" :data="editData" @done="query" />
      <type-form v-model="showTypeForm" :data="editData" @done="query" />
    </ele-page>
  </template>
  
  <script setup>
  import { ref, nextTick, watch } from 'vue'
  import { ElMessageBox } from 'element-plus/es'
  import { EleMessage } from 'ele-admin-plus/es'
  import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    SearchOutlined
  } from '@/components/icons'
  import { useMobile } from '@/utils/use-mobile'
  import TagList from './components/tag-list.vue'
  import TagForm from './components/tag-form.vue'
  import TypeForm from './components/type-form.vue'
  import { listTagType, removeTagType } from '@/api/system/tag'
  
  defineOptions({ name: 'SystemTag' })
  
  /** 是否是移动端 */
  const { mobile } = useMobile()
  
  /** 分割面板组件 */
  const splitRef = ref(null)
  
  /** 树组件 */
  const treeRef = ref(null)
  
  /** 加载状态 */
  const loading = ref(true)
  
  /** 树形数据 */
  const data = ref([])
  
  /** 选中数据 */
  const current = ref(null)
  
  /** 机构搜索关键字 */
  const keywords = ref('')
  
  /** 是否显示编辑弹窗 */
  const showTypeForm = ref(false)
  const showTagForm = ref(false)
  
  /** 编辑回显数据 */
  const editData = ref(null)
  
  /** 查询 */
  const query = () => {
    loading.value = true
    listTagType()
      .then((list) => {
        loading.value = false
        data.value = list ?? []
        nextTick(() => {
          handleNodeClick(data.value[0])
        })
      })
      .catch((e) => {
        loading.value = false
        EleMessage.error(e.message)
      })
  }
  
  /** 选择数据 */
  const handleNodeClick = (row) => {
    if (row && row.id) {
      current.value = row
      treeRef.value?.setCurrentKey?.(row.id)
    } else {
      current.value = null
    }
  }
  
  /** 打开编辑弹窗 */
  const openTypeEdit = (row) => {
    editData.value = row ?? null
    showTypeForm.value = true
  }

/** 打开编辑弹窗 */
const openTagEdit = (row) => {
editData.value = row ?? null
showTagForm.value = true
}
  
  /** 删除 */
  const remove = () => {
    const row = current.value
    if (!row) {
      return
    }
    ElMessageBox.confirm(`确定要删除“${row.name}”吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        })
        removeTagType({id: row.id})
          .then((msg) => {
            loading.close()
            EleMessage.success(msg)
            query()
          })
          .catch((e) => {
            loading.close()
            EleMessage.error(e.message)
          })
      })
      .catch(() => { })
  }
  
  /** 树过滤方法 */
  const filterNode = (value, data) => {
    if (value) {
      return !!(data.name && data.name.includes(value))
    }
    return true
  }
  
  /** 树过滤 */
  watch(keywords, (value) => {
    treeRef.value?.filter?.(value)
  })
  
  query();
  </script>
  