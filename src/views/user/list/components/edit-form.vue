<!-- 用户编辑弹窗 -->
<template>
  <ele-modal form :width="600" v-model="visible" title="修改用户" @open="handleOpen">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-row :gutter="16">
        <el-col :sm="24" :xs="24">
          <el-form-item label="用户账号" prop="username">
            <el-input :disabled="true" clearable :maxlength="100" v-model="form.username" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="用户昵称" prop="nickname">
            <el-input clearable :maxlength="100" v-model="form.nickname" placeholder="请输入用户昵称" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="用户头像" prop="image">
            <ele-upload-list
                v-model="form.image"
                :tools="true"
                @upload="handleUpload"
                @remove="handleRemove"
                @editUpload="handleEditUpload"
                :limit="1"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="openID" prop="weixinOpenid">
            <el-input clearable :maxlength="50" v-model="form.weixinOpenid" placeholder="请输入openID" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="用户邮箱" prop="email">
            <el-input type="email" clearable :maxlength="30" v-model="form.email" placeholder="请输入用户邮箱" />
          </el-form-item>
        </el-col>
        <el-col :sm="24" :xs="24">
          <el-form-item label="用户电话" prop="mobile">
            <el-input type="number" clearable :maxlength="30" v-model="form.mobile" placeholder="请输入用户电话" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { EleMessage } from 'ele-admin-plus/es'
import { useFormData } from '@/utils/use-form-data'
import { API_BASE_URL } from '@/config/setting.js'
import { fileUpload,updateUser } from '@/api/user/index'

const firmList = ref([])


const props = defineProps({
  /** 修改回显的数据 */
  data: Object,
})

const handleUpload = (item, retry) => {
  console.log(item.file,retry,'handleUpload')
  if (!retry) {
            form.image.push({ ...item });
        }
        const itemData = form.image.find((t) => t.key === item.key);
        if (!itemData) {
            return;
        }
        itemData.status = 'uploading';  // 设置为上传中状态
        itemData.progress = 0;
        fileUpload(item.file, {
            onUploadProgress: (e) => {
                // 更新上传进度
                if (e.total != null && itemData.status !== 'done') {
                    itemData.progress = (e.loaded / e.total) * 100;
                }
            }
        }).then((res) => {
            itemData.progress = 100;
            itemData.status = 'done';
            itemData.url = API_BASE_URL+res.url;  // 获取上传后的文件地址
            itemData.response = res;  // 保存接口的原始数据
        }).catch((e) => {
            // 设置为上传失败状态
            itemData.status = 'exception';
            console.error(e.message);
        });
}
const handleRemove = (item) => {
        // 一般删除都是直接删不会调接口, 因为对服务器来说硬盘是最便宜的
        //images.value.splice(images.value.indexOf(item), 1);
        // 如果需要调用接口删除可以在接口请求成功后再删除数据
        console.log(item)
        form.image.splice(form.image.indexOf(item), 1);
        // removeFile(item.response?.id).then((res) => {
        //     console.log(res);
        //     form.img.splice(form.img.indexOf(item), 1);
        // }).catch((e) => {
        //     console.error(e);
        // });
    };
     /** 修改时上传事件(如果开启了修改功能还需要处理修改按钮点击事件) */
     const handleEditUpload = ({ item, newItem }) => {
        const oldItem = form.image.find((t) => t.key === item.key);
        if (!oldItem) {
            return;
        }
        oldItem.url = void 0;
        oldItem.name = newItem.name;
        oldItem.file = newItem.file;
        oldItem.response = void 0;
        // 调用上传接口
        oldItem.status = 'uploading';  // 设置为上传中状态
        oldItem.progress = 0;
        fileUpload(newItem.file, {
            onUploadProgress: (e) => {
                // 更新上传进度
                if (e.total != null && oldItem.status !== 'done') {
                    oldItem.progress = (e.loaded / e.total) * 100;
                }
            }
        }).then((res) => {
            // 设置为上传完成状态
            oldItem.progress = 100;
            oldItem.status = 'done';
            oldItem.url = API_BASE_URL+res.url;  // 获取上传后的文件地址
            oldItem.response = res;  // 保存接口的原始数据
        }).catch((e) => {
            // 设置为上传失败状态
            oldItem.status = 'exception';
            console.error(e.message);
        });
    };

const emit = defineEmits(['done'])

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean })

/** 是否是修改 */
const isUpdate = ref(false)

/** 提交状态 */
const loading = ref(false)

/** 表单实例 */
const formRef = ref(null)

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  username: '',
  nickname:'',
  weixinOpenid:'',
  email:'',
  mobile: '',
  image:[],

})

/** 表单验证规则 */
const rules = reactive({
  nickname: [
    {
      required: true,
      message: '请输入用户昵称',
      type: 'string',
      trigger: 'blur'
    },
    {
      min: 2,
      message: '用户昵称长度最少为2位',
      type: 'string',
      trigger: 'blur'
    },
    {
      max: 100,
      message: '用户昵称长度最多为100位',
      type: 'string',
      trigger: 'blur'
    },
  ],
  weixinOpenid: [
    {
      required: true,
      message: '请输入openID',
      type: 'string',
      trigger: 'blur'
    }
  ],
  image: [
    {
      required: true,
      message: '请上传律师头像',
      type: 'array',
      trigger: 'change'
    }
  ],
})

const openEdit = () => {
  console.log('点击上传')
}
const removeImg = (id) => {
  form.image = form.image.filter(item => {
    return item.id !== id
  })
}

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false
}

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    form.avatar = {url:form.image[0].response.url}
    updateUser(form)
      .then((msg) => {
        loading.value = false
        EleMessage.success(msg)
        handleCancel()
        emit('done')
      })
      .catch((e) => {
        loading.value = false
        EleMessage.error(e.message)
      })
  })
}

/** 弹窗打开事件 */
const handleOpen = () => {
  if (props.data) {
    assignFields({ ...props.data, password: '' })
    isUpdate.value = true
  } else {
    resetFields()
    isUpdate.value = false
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.()
    })
  })
};
</script>
<style lang="scss" scoped>
.upload-img {
  position: relative;
  border: 1px dashed #d9d9d9;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
  margin: 10px;

  .goods-remove {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
  }
}
</style>