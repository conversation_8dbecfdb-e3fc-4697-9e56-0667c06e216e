<!-- 详情弹窗 -->
<template>
  <ele-modal title="用户详情" :width="600" v-model="visible">
    {{ data.auditStatus }}
    <!-- <el-space wrap v-if="data.auditStatus===0" style="margin-bottom:10px;">
      <el-button type="primary">通过审核</el-button>
      <el-button type="danger">驳回</el-button>
    </el-space> -->
    <el-descriptions v-if="data" label-width="120px" :border="true" :column="mobile ? 1 : 2" class="detail-table">
      <el-descriptions-item label="用户账号" :span="2">
        <div style="word-break: break-all">{{ data.username }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="用户昵称" :span="2">
        <div style="word-break: break-all">{{ data.nickname }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="头像" :span="2">
        <el-image style="width: auto; height: 60px" :src="API_BASE_URL+ data.avatar.url" />
      </el-descriptions-item>
      <el-descriptions-item label="openID" :span="2">
        <div>{{ data.weixinOpenid }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="邮箱" :span="2">
        <div>{{ data.email }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="手机号码" :span="2">
        <div>{{ data.mobile }}</div>
        <!-- <div>{{ data.tag_list }}</div> -->
      </el-descriptions-item>
      <el-descriptions-item label="上次登陆时间">
        <div>{{dayjs( data.logintime * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="上次登陆IP">
        <div>{{ data.loginip }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="注册时间">
        <div>{{dayjs( data.jointime * 1000).format('YYYY-MM-DD HH:mm:ss') }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="注册IP">
        <div>{{ data.joinip }}</div>
      </el-descriptions-item>
      
      
    </el-descriptions>
  </ele-modal>
</template>

<script setup>
import { reactive } from 'vue'
import { useMobile } from '@/utils/use-mobile'
import { formContextKey } from 'element-plus'
import dayjs from 'dayjs';

import {API_BASE_URL} from '@/config/setting.js'
defineProps({
  /** 修改回显的数据 */
  data: Object
})

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean })

/** 文字省略组件的提示组件的属性 */
const ellipsisTooltipProps = reactive({
  popperStyle: {
    width: '580px',
    maxWidth: '90%',
    wordBreak: 'break-all'
  },
  bodyStyle: {
    maxWidth: 'calc(100vw - 32px)',
    maxHeight: '252px',
    overflowY: 'auto',
    '--ele-scrollbar-color': '#5e5e5e',
    '--ele-scrollbar-hover-color': '#707070',
    '--ele-scrollbar-size': '8px'
  },
  offset: 4,
  placement: 'top'
})

/** 是否是移动端 */
const { mobile } = useMobile();
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 88px;
    text-align: right;
    font-weight: normal;
  }

  .detail-table :deep(.el-descriptions__content > div) {
    max-height: 100%;
  }
</style>
