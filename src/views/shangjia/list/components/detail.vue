<!-- 详情弹窗 -->
<template>
    <ele-modal title="商家详情" :width="600" v-model="visible">
      {{ data.auditStatus }}
      <!-- <el-space wrap v-if="data.auditStatus===0" style="margin-bottom:10px;">
        <el-button type="primary">通过审核</el-button>
        <el-button type="danger">驳回</el-button>
      </el-space> -->
      <el-descriptions v-if="data" :border="true" :column="mobile ? 1 : 2" class="detail-table">
        <el-descriptions-item label="商家名称" :span="2">
          <div style="word-break: break-all">{{ data.name }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="LOGO" :span="2">
          <el-image v-for="(item,index) in data.img" :key="index" style="width: auto; height: 60px;margin-right: 10px;" :src="API_BASE_URL+item.url" />
        </el-descriptions-item>
        <el-descriptions-item label="商家地址" :span="2">
          <div>{{ data.address }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="商家标签">
          <span v-for="(item,index) in data.tagList" :key="index" style="margin-right: 10px;">{{ item }}</span>
          <!-- <div>{{ data.tag_list }}</div> -->
        </el-descriptions-item>
        <el-descriptions-item label="商家电话">
          <div>{{ data.tel }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          <div>{{ data.createTime }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          <div>{{ data.updateTime }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="商家信息" :span="2">
          <div>{{ data.info }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </ele-modal>
  </template>
  
  <script setup>
  import { reactive } from 'vue'
  import { useMobile } from '@/utils/use-mobile'
  import { formContextKey } from 'element-plus'

  import {API_BASE_URL} from '@/config/setting.js'
  defineProps({
    /** 修改回显的数据 */
    data: Object
  })
  
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean })
  
  /** 文字省略组件的提示组件的属性 */
  const ellipsisTooltipProps = reactive({
    popperStyle: {
      width: '580px',
      maxWidth: '90%',
      wordBreak: 'break-all'
    },
    bodyStyle: {
      maxWidth: 'calc(100vw - 32px)',
      maxHeight: '252px',
      overflowY: 'auto',
      '--ele-scrollbar-color': '#5e5e5e',
      '--ele-scrollbar-hover-color': '#707070',
      '--ele-scrollbar-size': '8px'
    },
    offset: 4,
    placement: 'top'
  })
  
  /** 是否是移动端 */
  const { mobile } = useMobile();
  </script>
  
  <style lang="scss" scoped>
    .detail-table :deep(.el-descriptions__label) {
      width: 88px;
      text-align: right;
      font-weight: normal;
    }
  
    .detail-table :deep(.el-descriptions__content > div) {
      max-height: 100%;
    }
  </style>
  