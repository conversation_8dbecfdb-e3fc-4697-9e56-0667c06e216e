<template>
    <ele-page :flex-table="fixedHeight">
        <!-- 搜索表单 -->
        <search-form @search="handleSearch" />
        <ele-card :flex-table="fixedHeight" :body-style="{ paddingBottom: '4px' }" :style="{
            minHeight: fixedHeight ? '380px' : void 0,
            marginBottom: fixedHeight ? '0px' : void 0
        }">
            <!-- 提示信息 -->
            <ele-alert show-icon :closable="false" :style="{ marginBottom: toolDefault ? '12px' : '4px' }">
                <template #title>
                    <span>已选择&nbsp;</span>
                    <ele-text strong type="primary" tag="span">
                        {{ selections.length }}
                    </ele-text>
                    <span>&nbsp;项数据&emsp;</span>
                    <!-- <span>其中冻结状态的用户有 </span>
                    <b>{{selections.filter((d) => d.status === 1).length}} 个 &emsp;</b>
                    <el-link type="primary" :underline="false" style="vertical-align: 0px" @click="clearChoose">
                        清空
                    </el-link> -->
                </template>
            </ele-alert>
            <!-- 表格 -->
            <ele-pro-table ref="tableRef" row-key="id" :columns="columns" :datasource="datasource"
                v-model:current="current" v-model:selections="selections" :show-overflow-tooltip="true"
                :export-config="exportConfig" :print-config="{ datasource: exportSource }" :border="bordered"
                :sticky="!fixedHeight" :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
                :default-sort="{ prop: 'createTime', order: 'ascending' }" :footer-style="{ paddingBottom: '12px' }"
                style="padding-bottom: 0" class="demo-table" cache-key="listBasicTable" @done="handleDone">
                <template #toolbar>
                    <el-button type="primary" :icon="PlusOutlined" class="ele-btn-icon" @click="openEdit()">
                        新增律所
                    </el-button>
                    <ele-dropdown :disabled="!selections.length" :items="[
                        {
                            title: '批量删除',
                            command: 'del',
                            icon: DeleteOutlined,
                            danger: true,
                        }
                    ]" :icon-props="{ size: 15 }" placement="bottom-start" style="margin-left: 12px"
                        @command="handleDropClick">
                        <el-button :disabled="!selections.length" class="ele-btn-icon">
                            <span>批量操作</span>
                            <el-icon :size="12" style="margin: 0 -4px 0 2px">
                                <ArrowDown />
                            </el-icon>
                        </el-button>
                    </ele-dropdown>
                </template>
                <template #logo="{ row }">
                    <el-image style="width: 60px; height: 60px" :src="API_BASE_URL + row.logo.url" />
                </template>
                <!-- 表头工具按钮 -->
                <!-- <template #tools>
                    <div style="display: flex; align-items: center; flex-wrap: wrap">
                        <div>边框&nbsp;</div>
                        <el-switch v-model="bordered" size="small" />
                        <el-divider direction="vertical" />
                        <div>表头背景&nbsp;</div>
                        <el-switch v-model="toolDefault" size="small" />
                        <el-divider direction="vertical" />
                        <div>高度铺满&nbsp;</div>
                        <el-switch v-model="fixedHeight" size="small" />
                        <el-divider direction="vertical" />
                    </div>
                </template> -->

                <!-- 状态列 -->
                <template #auditStatus="{ row }">
                    <ele-dot v-if="row.auditStatus === 0" text="待审核" size="8px" />
                    <ele-dot v-else-if="row.auditStatus === 1" text="已审核" type="success" :ripple="false" size="8px" />
                    <ele-dot v-else-if="row.auditStatus === 2" text="审核未通过" type="danger" :ripple="false" size="8px" />
                </template>

                <!-- 操作列 -->
                <template #action="{ row }">
                    <el-link type="primary" :underline="false" @click.stop="openDetail(row)">
                        详情
                    </el-link>
                    <el-divider direction="vertical" />
                    <el-link type="primary" :underline="false" @click.stop="openEdit(row)">
                        修改
                    </el-link>
                    <el-divider direction="vertical" />
                    <el-link type="danger" :underline="false" @click.stop="remove(row)">
                        删除
                    </el-link>
                </template>

                <!-- 打印增加额外内容 -->
                <template #printTop>
                    <h2 style="text-align: center">还可以自定义打印的顶部区域</h2>
                </template>
                <template #printBottom="{ data }">
                    <h2 style="text-align: center">还可以自定义打印的底部区域</h2>
                    <div style="text-align: center">
                        共打印了 <b style="color: red">{{ data.length }}</b> 条数据
                    </div>
                </template>
            </ele-pro-table>
        </ele-card>
        <edit-form :data="current" v-model="showEdit" @done="reload" />
        <detail :data="current" v-model="showDetail" @done="reload" />
    </ele-page>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus/es'
import { EleMessage } from 'ele-admin-plus/es'
//   import { useI18n } from 'vue-i18n'
import {
    PlusOutlined,
    ArrowDown,
    CheckOutlined,
    DeleteOutlined
} from '@/components/icons'
import { getExportWorkbook } from '@/config/use-global-config'
import { download } from '@/utils/common'
import request from '@/utils/request'
import { usePageTab } from '@/utils/use-page-tab'
//   import { useDictData } from '@/utils/use-dict-data'
import SearchForm from './components/search-form.vue'
//   import { pageRecruit, listRecruit } from '@/api/recruit'
import { pageList, deleteFirm,batchDelFirm } from '@/api/falv/lvsuo'
import EditForm from './components/edit-form.vue'
import Detail from './components/detail.vue'
import { API_BASE_URL } from '@/config/setting.js'




//   defineOptions({ name: 'ListRecruit' })

//   const { t } = useI18n()
const { push } = useRouter()
const { addPageTab } = usePageTab()

/** 性别字典数据 */
//   const [sexDicts] = useDictData(['sex'])

/** 表格实例 */
const tableRef = ref(null)

/** 用户名筛选值 */
const nicknameFilterValue = ref('')

/** 表格搜索参数 */
const lastWhere = reactive({})

/** 表格列配置 */
const columns = computed(() => {
    return [
        {
            type: 'selection',
            columnKey: 'selection',
            width: 50,
            align: 'center',
            fixed: 'left'
        },
        {
            type: 'index',
            columnKey: 'index',
            width: 50,
            align: 'center',
            fixed: 'left'
        },
        {
            prop: 'firmName',
            label: '律所名称',
            minWidth: 110,
            align: 'center',
        },
        {
            prop: 'logo',
            label: '律所LOGO',
            minWidth: 110,
            align: 'center',
            slot: 'logo',
        },
        {
            prop: 'address',
            label: '律所地址',
            minWidth: 250,
            align: 'center',
        },
        {
            prop: 'tel',
            label: '律所电话',
            minWidth: 110,
            align: 'center',
        },
        {
            prop: 'createTime',
            label: '创建时间',
            minWidth: 200,
            align: 'center',
        },
        {
            columnKey: 'action',
            label: '操作',
            width: 180,
            align: 'center',
            showOverflowTooltip: false,
            resizable: false,
            slot: 'action',
            fixed: 'right',
            hideInPrint: true,
            hideInExport: true
        }
    ]
})

/** 表格选中数据 */
const selections = ref([])

/** 表格单选选中数据 */
const current = ref(null)

/** 是否显示编辑弹窗 */
const showEdit = ref(false)

/**是否显示详情弹窗 */
const showDetail = ref(false)

/** 表格是否显示边框 */
const bordered = ref(true)

/** 表头工具栏风格 */
const toolDefault = ref(true)

/** 表格固定高度 */
const fixedHeight = ref(false)

/** 表格数据源 */
const datasource = async ({ pages, where, orders, filters }) => {
    const result = await pageList({ ...where, ...orders, ...filters, ...pages })
    return result
}

/** 表格数据请求完成事件 */
const handleDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
        const ids = [45, 47, 48]
        tableRef.value?.setSelectedRowKeys?.(ids)
    })
}

/** 搜索事件 */
const handleSearch = (where) => {
    console.log(where)
    Object.assign(lastWhere, where)
    doReload()
}

/** 刷新表格 */
const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where })
}

/** 清空选择 */
const clearChoose = () => {
    tableRef.value?.clearSelection?.()
}

/** 打开编辑弹窗 */
const openEdit = (row) => {
    current.value = row ?? null
    if (current.value) {
        current.value.image = []
        current.value.image.push({
            key: 1,
            name: 'LOGO',
            url: API_BASE_URL + current.value.logo.url,
            status: 'done',
            response: current.value.logo.response?current.value.logo.response:current.value.logo // 接口返回的文件原始数据
        })
    }
    showEdit.value = true
}
/** 打开详情弹窗 */
const openDetail = (row) => {
    console.log(row)
    current.value = row ?? null
    showDetail.value = true
}
/** 删除 */
const remove = (row) => {
    ElMessageBox.confirm(`确定要删除${row.firmName}吗?`, '系统提示', {
        type: 'warning',
        draggable: true
    }).then(() => {
        deleteFirm({ id: row.id }).then(msg => {
            EleMessage.success(msg)
            reload()
        })
    })
        .catch(() => { })
}

/** 下拉按钮点击 */
const handleDropClick = (command) => {
    if (command === 'del') {
        let ids = selections.value.map(item => item.id)
        console.log(ids)
        batchDelFirm({ids}).then(res => {
            EleMessage.success(res)
            reload()
        })
    } 
}

/** 表格搜索 */
const doReload = () => {
    if (nicknameFilterValue.value) {
        reload({
            ...lastWhere,
            nickname: nicknameFilterValue.value
        })
    } else {
        reload(lastWhere)
    }
}

/** 用户名筛选事件 */
const handleNicknameFilter = (nickname) => {
    nicknameFilterValue.value = nickname
    doReload()
}

/** 导出和打印全部数据的数据源 */
const exportSource = ({ where, orders, filters }) => {
    return listRecruit({ ...where, ...orders, ...filters })
}

/** 导出配置 */
const exportConfig = reactive({
    fileName: '基础列表数据',
    datasource: exportSource,
    beforeExport: (params) => {
        const { fileName, closeModal, bodyCols, bodyData, headerData } = params
        const workbook = getExportWorkbook(params)
        const sheet = workbook.getWorksheet('Sheet1')

        const getBuffer = async () => {
            // 添加头像列图片
            const avatarColIndex = bodyCols.findIndex(
                (c) => c.dataKey === 'avatar'
            )
            if (sheet != null && avatarColIndex !== -1) {
                const avatarBuffers = await Promise.all(
                    bodyData.map(async (row) => {
                        const url = row[avatarColIndex].text
                        if (!url) {
                            return
                        }
                        const res = await request({ url, responseType: 'arraybuffer' })
                        return res.data
                    })
                )
                avatarBuffers.forEach((buffer, index) => {
                    const rowIndex = index + headerData.length
                    if (buffer != null) {
                        const imgId = workbook.addImage({ buffer, extension: 'png' })
                        sheet.addImage(imgId, {
                            tl: { col: avatarColIndex + 0.4, row: rowIndex + 0.2 },
                            ext: { width: 48, height: 48 }
                        })
                        sheet.getCell(rowIndex + 1, avatarColIndex + 1).value = ''
                    }
                    sheet.getRow(rowIndex + 1).height = 42
                    sheet.getColumn(avatarColIndex + 1).width = 8
                })
            }
            // 输出workbook
            const data = await workbook.xlsx.writeBuffer()
            return data
        }

        getBuffer().then((data) => {
            download(data, `${fileName}.xlsx`)
            closeModal()
        })
        return false
    }
});
</script>

<style lang="scss" scoped>
.demo-table :deep(td.demo-table-cell-avatar) {
    padding: 0;
    font-size: 0;
}
</style>