<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="律师名称">
            <el-input clearable v-model.trim="form.name" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="所在律所">
            <el-input clearable v-model.trim="form.lawFirmName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="律师电话">
            <el-input clearable v-model.trim="form.tel" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue'
import { useFormData } from '@/utils/use-form-data'
import { getTagList} from '@/api/shangjia/list'
const allTagList = ref([])
getTagList({ type: 0 }).then(res => {
  allTagList.value = res
  console.log(allTagList.value)
})

const emit = defineEmits(['search'])

/** 表单数据 */
const [form, resetFields] = useFormData({
  name: '',
  lawFirmName:'',
  tel: '',
})

/** 搜索 */
const search = () => {

  emit('search', {
    ...form,
  })
}

/**  重置 */
const reset = () => {
  resetFields()

  search()
}

</script>
