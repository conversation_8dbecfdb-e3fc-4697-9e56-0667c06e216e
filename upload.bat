@echo off
setlocal enabledelayedexpansion

REM windows script. Function: compile + upload package to server + restart service

REM Configuration parameters
set VUE_PROJECT_PATH=E:\code\html\dshr-care-frontend
set BUILD_OUTPUT=%VUE_PROJECT_PATH%\dist
set TAR_FILE=dist.tar.gz
set SSH_USER=root
set SSH_HOST=**************
set SSH_PORT=22
set REMOTE_DIR=/www/wwwroot/manage.care.dshr.top
set REMOTE_TAR_PATH=/tmp/%TAR_FILE%

REM 1. Enter project directory and install dependencies
echo [1/5] Installing dependencies...
cd /d "%VUE_PROJECT_PATH%"
if not exist "%VUE_PROJECT_PATH%" (
    echo Error: Project path does not exist: %VUE_PROJECT_PATH%
    exit /b 1
)

REM call npm install
REM if errorlevel 1 (
REM    echo Error: npm install failed
REM    exit /b 1
REM )

REM 2. Build production version
echo [2/5] Building production version...
call npm run build
if errorlevel 1 (
    echo Error: build failed
    exit /b 1
)

REM 3. Package as tar file
echo [3/5] Creating tar package...
if not exist "%BUILD_OUTPUT%" (
    echo Error: Build output directory does not exist: %BUILD_OUTPUT%
    exit /b 1
)

cd /d "%BUILD_OUTPUT%"
tar -czvf "..\%TAR_FILE%" *
if errorlevel 1 (
    echo Error: tar packaging failed
    cd /d "%VUE_PROJECT_PATH%"
    exit /b 1
)
cd /d "%VUE_PROJECT_PATH%"

REM 4. Upload to server via SCP
echo [4/5] Uploading to server...
if not exist "%TAR_FILE%" (
    echo Error: Tar file does not exist: %TAR_FILE%
    exit /b 1
)

scp -P %SSH_PORT% "%TAR_FILE%" %SSH_USER%@%SSH_HOST%:%REMOTE_TAR_PATH%
if errorlevel 1 (
    echo Error: upload failed
    exit /b 1
)

REM 5. Remote execution of decompression and permission setting
echo [5/5] Decompressing and setting permissions on server...
set "remote_cmd=tar -xzvf %REMOTE_TAR_PATH% -C %REMOTE_DIR% && chmod -R 755 %REMOTE_DIR% && rm %REMOTE_TAR_PATH%"
ssh -p %SSH_PORT% %SSH_USER%@%SSH_HOST% "%remote_cmd%"
if errorlevel 1 (
    echo Error: remote decompression failed
    exit /b 1
)

echo.
echo ========================================
echo Deploy completed successfully!
echo ========================================
endlocal
