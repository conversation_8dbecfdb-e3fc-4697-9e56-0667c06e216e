@echo off
setlocal enabledelayedexpansion

REM ========================================
REM Windows deployment script for Vue.js project (Skip npm install version)
REM Function: compile + upload package to server + restart service
REM 
REM This version skips npm install for faster deployment
REM ========================================

REM Configuration parameters
set VUE_PROJECT_PATH=E:\code\html\dshr-care-frontend
set BUILD_OUTPUT=%VUE_PROJECT_PATH%\dist
set TAR_FILE=dist.tar.gz
set SSH_USER=root
set SSH_HOST=**************
set SSH_PORT=22
set REMOTE_DIR=/www/wwwroot/manage.care.dshr.top
set REMOTE_TAR_PATH=/tmp/%TAR_FILE%

REM Control parameters - set to 1 to enable, 0 to disable
set SKIP_NPM_INSTALL=1

REM 1. Enter project directory and optionally install dependencies
echo [1/5] Preparing project...
cd /d "%VUE_PROJECT_PATH%"
if not exist "%VUE_PROJECT_PATH%" (
    echo Error: Project path does not exist: %VUE_PROJECT_PATH%
    exit /b 1
)

REM Check if npm install should be executed
if "%SKIP_NPM_INSTALL%"=="0" (
    echo Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo Error: npm install failed
        exit /b 1
    )
    echo Dependencies installed successfully
) else (
    echo Skipping npm install (SKIP_NPM_INSTALL=1)
)

REM 2. Clean existing build directory and build production version
echo [2/5] Cleaning and building production version...

REM Check if build output directory exists and remove it
if exist "%BUILD_OUTPUT%" (
    echo Removing existing build directory: %BUILD_OUTPUT%
    rmdir /s /q "%BUILD_OUTPUT%"
    if errorlevel 1 (
        echo Warning: Failed to remove existing build directory
    ) else (
        echo Successfully removed existing build directory
    )
)

call npm run build
if errorlevel 1 (
    echo Error: build failed
    exit /b 1
)
echo Build completed successfully

REM 3. Package as tar file
echo [3/5] Creating tar package...
if not exist "%BUILD_OUTPUT%" (
    echo Error: Build output directory does not exist: %BUILD_OUTPUT%
    exit /b 1
)

cd /d "%BUILD_OUTPUT%"
tar -czvf "..\%TAR_FILE%" *
if errorlevel 1 (
    echo Error: tar packaging failed
    cd /d "%VUE_PROJECT_PATH%"
    exit /b 1
)
cd /d "%VUE_PROJECT_PATH%"

REM 4. Upload to server via SCP
echo [4/5] Uploading to server...
if not exist "%TAR_FILE%" (
    echo Error: Tar file does not exist: %TAR_FILE%
    exit /b 1
)

scp -P %SSH_PORT% "%TAR_FILE%" %SSH_USER%@%SSH_HOST%:%REMOTE_TAR_PATH%
if errorlevel 1 (
    echo Error: upload failed
    exit /b 1
)

REM 5. Remote execution of decompression and permission setting
echo [5/5] Decompressing and setting permissions on server...
set "remote_cmd=tar -xzvf %REMOTE_TAR_PATH% -C %REMOTE_DIR% && chmod -R 755 %REMOTE_DIR% && rm %REMOTE_TAR_PATH%"
ssh -p %SSH_PORT% %SSH_USER%@%SSH_HOST% "%remote_cmd%"
if errorlevel 1 (
    echo Error: remote decompression failed
    exit /b 1
)

echo.
echo ========================================
echo Deploy completed successfully!
echo ========================================
endlocal
