# ========================================
# Deployment Configuration File
# ========================================

# Project Configuration
VUE_PROJECT_PATH="/path/to/your/dshr-care-frontend"
BUILD_OUTPUT_DIR="dist"
TAR_FILE_NAME="dist.tar.gz"

# Server Configuration
SSH_USER="root"
SSH_HOST="**************"
SSH_PORT="22"
REMOTE_DIR="/www/wwwroot/manage.care.dshr.top"
REMOTE_TEMP_DIR="/tmp"

# Deployment Options
SKIP_NPM_INSTALL=0  # 0 = run npm install, 1 = skip npm install
CLEAN_BUILD_DIR=1   # 0 = keep existing build, 1 = clean before build
CLEANUP_LOCAL_TAR=1 # 0 = keep local tar file, 1 = remove after upload

# Build Configuration
NODE_ENV="production"
BUILD_COMMAND="npm run build"

# SSH Configuration (optional)
# SSH_KEY_PATH="~/.ssh/id_rsa"
# SSH_CONFIG_FILE="~/.ssh/config"
