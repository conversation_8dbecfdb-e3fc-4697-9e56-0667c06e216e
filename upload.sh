#!/bin/bash

# ========================================
# Linux deployment script for Vue.js project
# Function: compile + upload package to server + restart service
# 
# Usage:
#   - To run with npm install: set SKIP_NPM_INSTALL=0 (default)
#   - To skip npm install: set SKIP_NPM_INSTALL=1
#   - Make executable: chmod +x upload.sh
#   - Run: ./upload.sh
# ========================================

# Exit on any error
set -e

# Configuration parameters
VUE_PROJECT_PATH="/path/to/your/dshr-care-frontend"  # Update this path
BUILD_OUTPUT="$VUE_PROJECT_PATH/dist"
TAR_FILE="dist.tar.gz"
SSH_USER="root"
SSH_HOST="**************"
SSH_PORT="22"
REMOTE_DIR="/www/wwwroot/manage.care.dshr.top"
REMOTE_TAR_PATH="/tmp/$TAR_FILE"

# Control parameters - set to 1 to enable, 0 to disable
SKIP_NPM_INSTALL=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required commands
check_dependencies() {
    local missing_deps=()
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists tar; then
        missing_deps+=("tar")
    fi
    
    if ! command_exists scp; then
        missing_deps+=("scp")
    fi
    
    if ! command_exists ssh; then
        missing_deps+=("ssh")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        print_info "Please install the missing dependencies and try again."
        exit 1
    fi
}

# Main deployment function
main() {
    print_info "Starting deployment process..."
    
    # Check dependencies
    check_dependencies
    
    # 1. Enter project directory and optionally install dependencies
    print_info "[1/5] Preparing project..."
    
    if [ ! -d "$VUE_PROJECT_PATH" ]; then
        print_error "Project path does not exist: $VUE_PROJECT_PATH"
        print_info "Please update the VUE_PROJECT_PATH variable in the script."
        exit 1
    fi
    
    cd "$VUE_PROJECT_PATH" || {
        print_error "Failed to change to project directory: $VUE_PROJECT_PATH"
        exit 1
    }
    
    # Check if npm install should be executed
    if [ "$SKIP_NPM_INSTALL" -eq 0 ]; then
        print_info "Installing dependencies..."
        if npm install; then
            print_success "Dependencies installed successfully"
        else
            print_error "npm install failed"
            exit 1
        fi
    else
        print_warning "Skipping npm install (SKIP_NPM_INSTALL=1)"
    fi
    
    # 2. Clean existing build directory and build production version
    print_info "[2/5] Cleaning and building production version..."
    
    # Check if build output directory exists and remove it
    if [ -d "$BUILD_OUTPUT" ]; then
        print_info "Removing existing build directory: $BUILD_OUTPUT"
        if rm -rf "$BUILD_OUTPUT"; then
            print_success "Successfully removed existing build directory"
        else
            print_warning "Failed to remove existing build directory"
        fi
    fi
    
    if npm run build; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
    
    # 3. Package as tar file
    print_info "[3/5] Creating tar package..."
    
    if [ ! -d "$BUILD_OUTPUT" ]; then
        print_error "Build output directory does not exist: $BUILD_OUTPUT"
        exit 1
    fi
    
    cd "$BUILD_OUTPUT" || {
        print_error "Failed to change to build output directory"
        exit 1
    }
    
    if tar -czf "../$TAR_FILE" .; then
        print_success "Tar package created successfully"
    else
        print_error "Tar packaging failed"
        cd "$VUE_PROJECT_PATH"
        exit 1
    fi
    
    cd "$VUE_PROJECT_PATH" || {
        print_error "Failed to return to project directory"
        exit 1
    }
    
    # 4. Upload to server via SCP
    print_info "[4/5] Uploading to server..."
    
    if [ ! -f "$TAR_FILE" ]; then
        print_error "Tar file does not exist: $TAR_FILE"
        exit 1
    fi
    
    if scp -P "$SSH_PORT" "$TAR_FILE" "$SSH_USER@$SSH_HOST:$REMOTE_TAR_PATH"; then
        print_success "Upload completed successfully"
    else
        print_error "Upload failed"
        exit 1
    fi
    
    # 5. Remote execution of decompression and permission setting
    print_info "[5/5] Decompressing and setting permissions on server..."
    
    remote_cmd="tar -xzf $REMOTE_TAR_PATH -C $REMOTE_DIR && chmod -R 755 $REMOTE_DIR && rm $REMOTE_TAR_PATH"
    
    if ssh -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" "$remote_cmd"; then
        print_success "Remote decompression and permission setting completed"
    else
        print_error "Remote decompression failed"
        exit 1
    fi
    
    # Clean up local tar file
    if [ -f "$TAR_FILE" ]; then
        rm "$TAR_FILE"
        print_info "Cleaned up local tar file"
    fi
    
    echo
    echo "========================================"
    print_success "Deploy completed successfully!"
    echo "========================================"
}

# Run main function
main "$@"
