#!/bin/bash

# ========================================
# Advanced Linux deployment script for Vue.js project
# Function: compile + upload package to server + restart service
# 
# Usage:
#   chmod +x deploy.sh
#   ./deploy.sh [config_file]
#   
# Examples:
#   ./deploy.sh                    # Use default deploy.config
#   ./deploy.sh custom.config      # Use custom config file
#   ./deploy.sh --skip-install     # Skip npm install
#   ./deploy.sh --help             # Show help
# ========================================

# Exit on any error
set -e

# Default configuration file
DEFAULT_CONFIG="deploy.config"
CONFIG_FILE=""
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}$1${NC}"
}

# Show help
show_help() {
    cat << EOF
Advanced Vue.js Deployment Script

Usage: $0 [OPTIONS] [CONFIG_FILE]

OPTIONS:
    --skip-install    Skip npm install step
    --help           Show this help message

CONFIG_FILE:
    Path to configuration file (default: deploy.config)

Examples:
    $0                          # Use default config
    $0 production.config        # Use custom config
    $0 --skip-install          # Skip npm install
    $0 --skip-install prod.config  # Skip install with custom config

Configuration file format:
    VUE_PROJECT_PATH="/path/to/project"
    SSH_USER="username"
    SSH_HOST="server.com"
    # ... see deploy.config for full example

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-install)
                SKIP_NPM_INSTALL=1
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            -*)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                CONFIG_FILE="$1"
                shift
                ;;
        esac
    done
    
    # Set default config file if not specified
    if [ -z "$CONFIG_FILE" ]; then
        CONFIG_FILE="$DEFAULT_CONFIG"
    fi
    
    # Make config file path absolute if it's relative
    if [[ ! "$CONFIG_FILE" = /* ]]; then
        CONFIG_FILE="$SCRIPT_DIR/$CONFIG_FILE"
    fi
}

# Load configuration
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "Configuration file not found: $CONFIG_FILE"
        print_info "Please create a configuration file or specify a valid path."
        print_info "See deploy.config for an example."
        exit 1
    fi
    
    print_info "Loading configuration from: $CONFIG_FILE"
    
    # Source the configuration file
    # shellcheck source=/dev/null
    source "$CONFIG_FILE"
    
    # Set derived variables
    BUILD_OUTPUT="$VUE_PROJECT_PATH/$BUILD_OUTPUT_DIR"
    REMOTE_TAR_PATH="$REMOTE_TEMP_DIR/$TAR_FILE_NAME"
    
    # Validate required variables
    local required_vars=(
        "VUE_PROJECT_PATH"
        "SSH_USER"
        "SSH_HOST"
        "SSH_PORT"
        "REMOTE_DIR"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "Required configuration variable '$var' is not set"
            exit 1
        fi
    done
    
    print_success "Configuration loaded successfully"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required commands
check_dependencies() {
    local missing_deps=()
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists tar; then
        missing_deps+=("tar")
    fi
    
    if ! command_exists scp; then
        missing_deps+=("scp")
    fi
    
    if ! command_exists ssh; then
        missing_deps+=("ssh")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        print_info "Please install the missing dependencies and try again."
        exit 1
    fi
}

# Main deployment function
main() {
    print_step "========================================="
    print_step "Starting Vue.js Deployment Process"
    print_step "========================================="
    
    # Parse arguments and load config
    parse_args "$@"
    load_config
    
    # Check dependencies
    check_dependencies
    
    # 1. Enter project directory and optionally install dependencies
    print_step "[1/5] Preparing project..."
    
    if [ ! -d "$VUE_PROJECT_PATH" ]; then
        print_error "Project path does not exist: $VUE_PROJECT_PATH"
        print_info "Please update the VUE_PROJECT_PATH in your configuration file."
        exit 1
    fi
    
    cd "$VUE_PROJECT_PATH" || {
        print_error "Failed to change to project directory: $VUE_PROJECT_PATH"
        exit 1
    }
    
    # Check if npm install should be executed
    if [ "${SKIP_NPM_INSTALL:-0}" -eq 0 ]; then
        print_info "Installing dependencies..."
        if npm install; then
            print_success "Dependencies installed successfully"
        else
            print_error "npm install failed"
            exit 1
        fi
    else
        print_warning "Skipping npm install"
    fi
    
    # 2. Clean existing build directory and build production version
    print_step "[2/5] Building production version..."
    
    # Check if build output directory should be cleaned
    if [ "${CLEAN_BUILD_DIR:-1}" -eq 1 ] && [ -d "$BUILD_OUTPUT" ]; then
        print_info "Removing existing build directory: $BUILD_OUTPUT"
        if rm -rf "$BUILD_OUTPUT"; then
            print_success "Successfully removed existing build directory"
        else
            print_warning "Failed to remove existing build directory"
        fi
    fi
    
    # Set NODE_ENV if specified
    if [ -n "${NODE_ENV:-}" ]; then
        export NODE_ENV
        print_info "NODE_ENV set to: $NODE_ENV"
    fi
    
    if eval "${BUILD_COMMAND:-npm run build}"; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
    
    # 3. Package as tar file
    print_step "[3/5] Creating tar package..."
    
    if [ ! -d "$BUILD_OUTPUT" ]; then
        print_error "Build output directory does not exist: $BUILD_OUTPUT"
        exit 1
    fi
    
    cd "$BUILD_OUTPUT" || {
        print_error "Failed to change to build output directory"
        exit 1
    }
    
    if tar -czf "../$TAR_FILE_NAME" .; then
        print_success "Tar package created successfully"
    else
        print_error "Tar packaging failed"
        cd "$VUE_PROJECT_PATH"
        exit 1
    fi
    
    cd "$VUE_PROJECT_PATH" || {
        print_error "Failed to return to project directory"
        exit 1
    }
    
    # 4. Upload to server via SCP
    print_step "[4/5] Uploading to server..."
    
    if [ ! -f "$TAR_FILE_NAME" ]; then
        print_error "Tar file does not exist: $TAR_FILE_NAME"
        exit 1
    fi
    
    # Build SCP command with optional SSH key
    scp_cmd="scp -P $SSH_PORT"
    if [ -n "${SSH_KEY_PATH:-}" ]; then
        scp_cmd="$scp_cmd -i $SSH_KEY_PATH"
    fi
    
    if $scp_cmd "$TAR_FILE_NAME" "$SSH_USER@$SSH_HOST:$REMOTE_TAR_PATH"; then
        print_success "Upload completed successfully"
    else
        print_error "Upload failed"
        exit 1
    fi
    
    # 5. Remote execution of decompression and permission setting
    print_step "[5/5] Deploying on server..."
    
    remote_cmd="tar -xzf $REMOTE_TAR_PATH -C $REMOTE_DIR && chmod -R 755 $REMOTE_DIR && rm $REMOTE_TAR_PATH"
    
    # Build SSH command with optional SSH key
    ssh_cmd="ssh -p $SSH_PORT"
    if [ -n "${SSH_KEY_PATH:-}" ]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY_PATH"
    fi
    
    if $ssh_cmd "$SSH_USER@$SSH_HOST" "$remote_cmd"; then
        print_success "Remote deployment completed successfully"
    else
        print_error "Remote deployment failed"
        exit 1
    fi
    
    # Clean up local tar file if configured
    if [ "${CLEANUP_LOCAL_TAR:-1}" -eq 1 ] && [ -f "$TAR_FILE_NAME" ]; then
        rm "$TAR_FILE_NAME"
        print_info "Cleaned up local tar file"
    fi
    
    print_step "========================================="
    print_success "Deployment completed successfully!"
    print_step "========================================="
}

# Run main function with all arguments
main "$@"
